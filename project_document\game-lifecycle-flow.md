# 狼人杀游戏完整生命周期流程图

**版本：** v0.6.0  
**创建日期：** 2025-01-16  
**基于：** apps/GameStart.js, model/services/GameLobby.js, model/core/Game.js, model/core/VictoryChecker.js

## 概述

本文档基于游戏启动流程、大厅管理、游戏核心生命周期和胜利条件检查等模块，使用 Mermaid 语法创建完整的游戏生命周期流程图，展示从游戏创建到结束的全流程。

## 1. 完整游戏生命周期主流程图

### 1.1 游戏生命周期概览

```mermaid
flowchart TD
    A[用户发起 #创建狼人杀] --> B{检查群状态}
    B -->|群内已有游戏| C[提示已有游戏进行中]
    B -->|群内无游戏| D[创建GameLobby]
    
    D --> E[创建者自动加入]
    E --> F[等待其他玩家加入]
    
    F --> G[用户发起 #加入狼人杀]
    G --> H{检查加入条件}
    H -->|游戏已开始| I[提示无法加入]
    H -->|玩家已在游戏中| J[提示已在游戏中]
    H -->|人数已满| K[提示人数已满]
    H -->|条件满足| L[玩家加入成功]
    
    L --> M{是否继续等待}
    M -->|是| F
    M -->|否| N[用户发起 #开始狼人杀]
    
    N --> O{检查开始条件}
    O -->|人数不足| P[提示人数不足]
    O -->|条件满足| Q[创建Game实例]
    
    Q --> R[初始化游戏组件]
    R --> S[角色分配]
    S --> T[游戏状态初始化]
    T --> U[进入游戏循环]
    
    U --> V[夜晚阶段]
    V --> W[白天阶段]
    W --> X[投票阶段]
    X --> Y[遗言阶段]
    Y --> Z{检查胜利条件}
    
    Z -->|游戏继续| AA[回合数+1]
    AA --> V
    Z -->|游戏结束| BB[宣布胜利结果]
    
    BB --> CC[更新玩家统计]
    CC --> DD[清理游戏资源]
    DD --> EE[移除游戏注册]
    EE --> FF[游戏生命周期结束]
    
    C --> FF
    I --> FF
    J --> FF
    K --> FF
    P --> FF
    
    style A fill:#4299e1,stroke:#3182ce,color:#fff
    style FF fill:#2d3748,stroke:#4a5568,color:#fff
    style Q fill:#68d391,stroke:#38a169,color:#000
    style BB fill:#fc8181,stroke:#e53e3e,color:#fff
```

### 1.2 详细游戏初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as GameStart.js
    participant Lobby as GameLobby
    participant Registry as GameRegistry
    participant Game as Game.js
    participant PM as PlayerManager
    participant SM as StateManager
    participant VC as VictoryChecker

    Note over User,App: 游戏创建阶段
    User->>App: #创建狼人杀
    App->>App: 检查群状态
    App->>Lobby: new GameLobby(GameConfig)
    App->>Lobby: addPlayer(creator)
    App-->>User: 大厅创建成功

    Note over User,App: 玩家加入阶段
    loop 玩家加入过程
        User->>App: #加入狼人杀
        App->>Lobby: hasPlayer(playerId)
        App->>Lobby: addPlayer(player)
        App-->>User: 加入成功
    end

    Note over User,Game: 游戏启动阶段
    User->>App: #开始狼人杀
    App->>Lobby: createGame(groupId, e)
    Lobby->>Game: new Game(config)
    Game->>PM: new PlayerManager(game)
    Game->>SM: new StateManager(game, stateMachine)
    Game->>VC: new VictoryChecker()
    Lobby->>Registry: addGame(groupId, game)
    
    App->>Game: start()
    Game->>Game: 检查玩家数量
    Game->>PM: initPlayers()
    PM->>PM: 角色分配和初始化
    Game->>SM: initializeState()
    SM->>SM: 进入NightState
    Game->>Game: emit('gameStart')
    App-->>User: 游戏开始!

    Note over Game: 游戏循环阶段
    loop 游戏主循环
        Game->>SM: 状态转换
        SM->>SM: 执行状态逻辑
        Game->>VC: checkVictory()
        alt 游戏继续
            VC-->>Game: {gameOver: false}
        else 游戏结束
            VC-->>Game: {gameOver: true, winner, reason}
            break 退出循环
        end
    end

    Note over Game,Registry: 游戏结束阶段
    Game->>Game: emit('gameEnd', result)
    Game->>Game: cleanup()
    Registry->>Registry: removeGame(groupId)
    App-->>User: 游戏结束，宣布结果
```

## 2. 游戏状态循环详细流程

### 2.1 游戏主循环状态转换

```mermaid
stateDiagram-v2
    [*] --> GameLobby : 创建大厅
    GameLobby --> WaitingPlayers : 等待玩家
    WaitingPlayers --> WaitingPlayers : 玩家加入
    WaitingPlayers --> GameInitialization : 开始游戏
    
    GameInitialization --> NightState : 初始化完成
    
    state GameLoop {
        NightState --> DayState : 夜晚结束
        DayState --> VoteState : 白天讨论结束
        DayState --> SheriffElectState : 首日警长选举
        SheriffElectState --> DayState : 选举结束
        VoteState --> LastWordsState : 有人被投票出局
        VoteState --> NightState : 平票无人出局
        LastWordsState --> NightState : 遗言结束
        LastWordsState --> SheriffTransferState : 警长死亡
        SheriffTransferState --> NightState : 警徽移交完成
        
        NightState --> VictoryCheck : 每个状态结束后
        DayState --> VictoryCheck
        VoteState --> VictoryCheck
        LastWordsState --> VictoryCheck
        
        VictoryCheck --> NightState : 游戏继续
        VictoryCheck --> GameEnd : 达成胜利条件
    }
    
    GameEnd --> ResourceCleanup : 宣布结果
    ResourceCleanup --> [*] : 清理完成
```

### 2.2 胜利条件检查流程

```mermaid
flowchart TD
    A[触发胜利检查] --> B[WolfExtinctionChecker]
    B --> C{狼人全部死亡?}
    C -->|是| D[好人胜利]
    C -->|否| E[GoodPeopleExtinctionChecker]
    
    E --> F{好人全部死亡?}
    F -->|是| G[狼人胜利]
    F -->|否| H[GodsExtinctionChecker]
    
    H --> I{屠边规则开启?}
    I -->|否| M[VillagersExtinctionChecker]
    I -->|是| J{神职全部死亡?}
    J -->|是| K[狼人胜利-屠神]
    J -->|否| M
    
    M --> N{屠边规则开启?}
    N -->|否| P[游戏继续]
    N -->|是| O{平民全部死亡?}
    O -->|是| Q[狼人胜利-屠民]
    O -->|否| P
    
    D --> R[游戏结束]
    G --> R
    K --> R
    Q --> R
    P --> S[返回游戏循环]
    
    style D fill:#68d391,stroke:#38a169,color:#000
    style G fill:#fc8181,stroke:#e53e3e,color:#fff
    style K fill:#fc8181,stroke:#e53e3e,color:#fff
    style Q fill:#fc8181,stroke:#e53e3e,color:#fff
    style P fill:#4299e1,stroke:#3182ce,color:#fff
```

## 3. 错误处理和异常路径

### 3.1 游戏创建阶段错误处理

```mermaid
flowchart TD
    A[创建游戏请求] --> B{群状态检查}
    B -->|群内已有游戏| C[返回错误：已有游戏进行中]
    B -->|群内有大厅| D[返回错误：已有大厅等待中]
    B -->|状态正常| E[创建GameLobby]
    
    E --> F{创建是否成功}
    F -->|失败| G[返回错误：创建失败]
    F -->|成功| H[添加创建者]
    
    H --> I{添加是否成功}
    I -->|失败| J[清理大厅资源]
    I -->|成功| K[返回成功]
    
    J --> L[返回错误：初始化失败]
    
    C --> M[记录错误日志]
    D --> M
    G --> M
    L --> M
    M --> N[结束流程]
    
    K --> O[正常流程继续]
```

### 3.2 游戏运行时异常处理

```mermaid
sequenceDiagram
    participant Game as Game
    participant SM as StateManager
    participant PM as PlayerManager
    participant Error as ErrorHandler

    Note over Game: 运行时异常监控
    
    Game->>SM: 状态转换请求
    SM->>SM: 验证转换合法性
    alt 转换失败
        SM->>Error: 记录状态转换错误
        Error->>Game: 尝试状态恢复
        Game->>Game: 回滚到稳定状态
    end
    
    Game->>PM: 玩家操作请求
    PM->>PM: 验证操作合法性
    alt 操作失败
        PM->>Error: 记录玩家操作错误
        Error->>Game: 通知操作失败
        Game->>Game: 继续等待有效操作
    end
    
    Game->>Game: 检查游戏完整性
    alt 发现数据异常
        Game->>Error: 记录数据异常
        Error->>Game: 触发数据修复
        Game->>Game: 尝试自动修复
        alt 修复失败
            Game->>Game: 强制结束游戏
            Game->>Game: 清理资源
        end
    end
```

## 4. 资源管理和清理流程

### 4.1 游戏资源清理流程

```mermaid
flowchart TD
    A[游戏结束触发] --> B[Game.cleanup()]
    B --> C[清理事件监听器]
    C --> D[清理GameEventHandler]
    D --> E[清理PlayerManager]
    E --> F[清理StateManager]
    F --> G[清理错误历史]
    
    E --> E1[清理玩家数据]
    E1 --> E2[清理角色数据]
    E2 --> E3[清理缓存系统]
    E3 --> E4[清理角色静态数据]
    
    F --> F1[清理状态历史]
    F1 --> F2[清理状态机]
    F2 --> F3[清理状态上下文]
    
    G --> H[GameRegistry.removeGame()]
    H --> I[lobbies.delete()]
    I --> J[更新玩家统计]
    J --> K[记录清理日志]
    K --> L[资源清理完成]
    
    style A fill:#fc8181,stroke:#e53e3e,color:#fff
    style L fill:#68d391,stroke:#38a169,color:#000
```

### 4.2 内存泄漏防护机制

```mermaid
sequenceDiagram
    participant Game as Game
    participant PM as PlayerManager
    participant SM as StateManager
    participant WR as WolfRole
    participant Timer as 清理定时器

    Note over Game: 内存泄漏防护
    
    Game->>Game: 游戏结束检测
    Game->>Timer: 设置延迟清理(2秒)
    
    Timer->>Game: 触发cleanup()
    Game->>Game: removeAllListeners()
    Game->>PM: cleanup()
    PM->>WR: WolfRole.cleanup()
    WR->>WR: 清理静态数据
    
    Game->>SM: cleanup()
    SM->>SM: 清理状态历史
    SM->>SM: 清理状态机引用
    
    Game->>Game: 清理错误历史
    Game->>Game: 重置所有引用为null
    
    Note over Game: 防护措施
    Game->>Game: try-catch包装所有清理操作
    Game->>Game: 记录清理过程日志
    Game->>Game: 验证清理完整性
```

## 5. 关键决策点和分支条件

### 5.1 游戏流程关键决策点

| 决策点 | 条件检查 | 分支路径 | 影响 |
|--------|----------|----------|------|
| 创建游戏 | `!GameRegistry.hasGame() && !lobbies.has()` | 成功/失败 | 是否能创建新游戏 |
| 加入游戏 | `!GameRegistry.hasGame() && lobby.exists() && !lobby.hasPlayer()` | 成功/失败 | 玩家是否能加入 |
| 开始游戏 | `playerCount >= config.minPlayers` | 成功/失败 | 游戏是否能开始 |
| 状态转换 | `isValidTransition(fromState, toState)` | 允许/拒绝 | 游戏流程是否正确 |
| 胜利检查 | `各种胜利条件检查器` | 继续/结束 | 游戏是否结束 |
| 资源清理 | `游戏结束后2秒` | 自动触发 | 防止内存泄漏 |

### 5.2 配置驱动的分支逻辑

```mermaid
flowchart TD
    A[读取游戏配置] --> B{最小玩家数检查}
    B -->|不足| C[拒绝开始]
    B -->|满足| D{最大玩家数检查}
    D -->|超出| E[拒绝加入]
    D -->|未超出| F{屠边规则检查}
    
    F -->|开启| G[启用屠边胜利条件]
    F -->|关闭| H[使用标准胜利条件]
    
    G --> I{女巫自救检查}
    H --> I
    I -->|允许| J[女巫可以自救]
    I -->|禁止| K[女巫不能自救]
    
    J --> L{狼人自爆检查}
    K --> L
    L -->|允许| M[狼人可以自爆]
    L -->|禁止| N[狼人不能自爆]
    
    M --> O[应用所有配置]
    N --> O
    O --> P[开始游戏]
```

## 总结

狼人杀游戏的完整生命周期设计具有以下特点：

1. **清晰的阶段划分**：大厅创建 → 玩家加入 → 游戏初始化 → 游戏循环 → 结束清理
2. **完善的错误处理**：每个阶段都有相应的异常处理和恢复机制
3. **严格的状态管理**：通过状态机确保游戏流程的正确性和可预测性
4. **灵活的胜利条件**：支持多种胜利条件和配置驱动的规则变化
5. **完善的资源管理**：防止内存泄漏的清理机制和资源监控
6. **可扩展的架构**：支持新功能和新规则的添加

这种设计确保了游戏的稳定性、可维护性和用户体验，为复杂的多人在线游戏提供了坚实的技术基础。
