# 开发指南和最佳实践

**版本：** v0.6.0  
**更新日期：** 2025-01-16  
**适用范围：** 狼人杀插件开发团队

## 概述

本指南提供了狼人杀插件开发的最佳实践、编码规范和开发流程，帮助开发者编写高质量、可维护的代码。

## 开发环境设置

### 必需工具

- **Node.js**: ≥16.0.0
- **npm**: ≥8.0.0
- **Git**: ≥2.30.0
- **VSCode**: 推荐IDE

### 推荐扩展

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-jest",
    "bradlc.vscode-tailwindcss"
  ]
}
```

### 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd werewolf-plugin

# 安装依赖
npm install

# 运行测试
npm test

# 启动开发模式
npm run dev
```

## 编码规范

### JavaScript/ES6+ 规范

#### 1. 命名约定

```javascript
// 类名：PascalCase
class PlayerManager {}

// 函数/变量：camelCase
const playerCount = 10;
function handlePlayerAction() {}

// 常量：SCREAMING_SNAKE_CASE
const MAX_PLAYERS = 12;
const GAME_PHASES = { NIGHT: 'night' };

// 私有方法：下划线前缀
class Game {
  _invalidateCache() {}
}
```

#### 2. 文件命名

```
// 类文件：PascalCase.js
PlayerManager.js
StateManager.js

// 工具文件：camelCase.js
validationUtils.js
errorHandler.js

// 常量文件：kebab-case.js
game-constants.js
error-codes.js
```

#### 3. 导入/导出规范

```javascript
// 具名导出（推荐）
export class PlayerManager {}
export const ROLES = {};

// 导入
import { PlayerManager, ROLES } from './PlayerManager.js';

// 默认导出（仅用于主要类）
export default class Game {}
import Game from './Game.js';
```

### 代码结构规范

#### 1. 类结构顺序

```javascript
class ExampleClass {
  // 1. 静态属性
  static defaultConfig = {};
  
  // 2. 实例属性
  constructor() {
    this.property = value;
  }
  
  // 3. 静态方法
  static createInstance() {}
  
  // 4. 公共方法（按重要性排序）
  publicMethod() {}
  
  // 5. 私有方法
  _privateMethod() {}
  
  // 6. Getter/Setter
  get property() {}
  set property(value) {}
}
```

#### 2. 函数结构

```javascript
/**
 * 函数描述
 * @param {Type} param 参数描述
 * @returns {Type} 返回值描述
 */
function exampleFunction(param) {
  // 1. 参数验证
  if (!param) {
    throw new Error('参数不能为空');
  }
  
  // 2. 变量声明
  const result = {};
  
  // 3. 主要逻辑
  // ...
  
  // 4. 返回结果
  return result;
}
```

## 架构设计原则

### 1. 单一职责原则 (SRP)

```javascript
// ❌ 错误：一个类承担多个职责
class Game {
  addPlayer() {}
  removePlayer() {}
  changeState() {}
  handleAction() {}
  sendMessage() {}
}

// ✅ 正确：职责分离
class PlayerManager {
  addPlayer() {}
  removePlayer() {}
}

class StateManager {
  changeState() {}
  handleAction() {}
}

class EventHandler {
  sendMessage() {}
}
```

### 2. 依赖注入原则

```javascript
// ❌ 错误：硬编码依赖
class Game {
  constructor() {
    this.playerManager = new PlayerManager(); // 硬依赖
  }
}

// ✅ 正确：依赖注入
class Game {
  constructor({ playerManager, stateManager }) {
    this.playerManager = playerManager;
    this.stateManager = stateManager;
  }
}
```

### 3. 事件驱动设计

```javascript
// ✅ 使用事件进行组件通信
class PlayerManager extends EventEmitter {
  handlePlayerDeath(player) {
    player.isAlive = false;
    this.emit('playerDeath', { player });
  }
}

class Game {
  constructor() {
    this.playerManager.on('playerDeath', this.handlePlayerDeath.bind(this));
  }
}
```

## 错误处理最佳实践

### 1. 统一错误处理

```javascript
// ✅ 使用统一的错误处理器
import { ErrorHandler } from './ErrorHandler.js';

class SomeService {
  async doSomething() {
    try {
      // 业务逻辑
    } catch (error) {
      return ErrorHandler.handle(error, { context: 'doSomething' });
    }
  }
}
```

### 2. 错误分类

```javascript
// ✅ 使用标准错误代码
const result = ValidationUtils.validatePlayer(player);
if (!result.isValid) {
  throw new GameError('玩家验证失败', 'E1100', { playerId: player.id });
}
```

### 3. 用户友好的错误消息

```javascript
// ✅ 提供用户友好的错误消息
const friendlyMessages = {
  'E1100': '请确认您是游戏参与者',
  'E1101': '请确认游戏号码',
  'E1200': '当前阶段无法执行该操作'
};
```

## 测试最佳实践

### 1. 测试结构

```javascript
describe('PlayerManager', () => {
  let playerManager;
  let mockGame;

  beforeEach(() => {
    mockGame = createMockGame();
    playerManager = new PlayerManager(mockGame);
  });

  describe('addPlayer方法', () => {
    test('应该成功添加新玩家', () => {
      // Arrange
      const player = createMockPlayer();
      
      // Act
      const result = playerManager.addPlayer(player);
      
      // Assert
      expect(result).toBe(true);
      expect(playerManager.hasPlayer(player.id)).toBe(true);
    });
  });
});
```

### 2. 模拟对象使用

```javascript
// ✅ 使用全局模拟对象工厂
const mockPlayer = createMockPlayer('player1', {
  name: 'TestPlayer',
  isAlive: true
});

const mockGame = createMockGame({
  playerManager: {
    getPlayerCount: jest.fn(() => 4)
  }
});
```

### 3. 异步测试

```javascript
// ✅ 正确处理异步测试
test('应该正确处理异步操作', async () => {
  const promise = playerManager.initializePlayerRoles(['WOLF', 'VILLAGER']);
  await expect(promise).resolves.toBeUndefined();
});
```

## 性能优化指南

### 1. 缓存策略

```javascript
class PlayerManager {
  constructor() {
    this._cache = new Map();
  }
  
  getAlivePlayers(options = {}) {
    const cacheKey = JSON.stringify(options);
    
    if (this._cache.has(cacheKey)) {
      return this._cache.get(cacheKey);
    }
    
    const result = this._computeAlivePlayers(options);
    this._cache.set(cacheKey, result);
    return result;
  }
  
  _invalidateCache() {
    this._cache.clear();
  }
}
```

### 2. 内存管理

```javascript
class Game {
  cleanup() {
    // 清理事件监听器
    this.removeAllListeners();
    
    // 清理子组件
    this.playerManager.cleanup();
    this.stateManager.cleanup();
    
    // 清理引用
    this.playerManager = null;
    this.stateManager = null;
  }
}
```

### 3. 批量操作

```javascript
// ✅ 批量处理减少开销
class ValidationUtils {
  static validateBatch(validators) {
    const errors = [];
    
    for (const validator of validators) {
      const result = validator();
      if (!result.isValid) {
        errors.push(result.error);
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }
}
```

## 安全编码实践

### 1. 输入验证

```javascript
// ✅ 严格的输入验证
function processGameNumber(input) {
  // 类型检查
  if (typeof input !== 'number' && typeof input !== 'string') {
    throw new GameError('游戏号码必须是数字或字符串', 'E1101');
  }
  
  // 范围检查
  const number = parseInt(input);
  if (isNaN(number) || number < 1 || number > 20) {
    throw new GameError('游戏号码必须在1-20之间', 'E1101');
  }
  
  return number;
}
```

### 2. 敏感信息保护

```javascript
// ✅ 不在日志中暴露敏感信息
function logPlayerAction(player, action) {
  console.log(`玩家${player.gameNumber}执行了${action}`, {
    playerId: player.id, // 不记录真实姓名
    action,
    timestamp: Date.now()
  });
}
```

## 代码审查检查清单

### 提交前检查

- [ ] 代码符合命名规范
- [ ] 函数长度不超过50行
- [ ] 类的方法数量不超过20个
- [ ] 添加了必要的注释和文档
- [ ] 编写了对应的单元测试
- [ ] 测试覆盖率达到90%以上
- [ ] 没有ESLint错误或警告
- [ ] 性能敏感代码进行了优化

### 功能检查

- [ ] 错误处理完善
- [ ] 边界条件考虑周全
- [ ] 输入验证严格
- [ ] 资源正确清理
- [ ] 事件监听器正确移除

### 安全检查

- [ ] 用户输入经过验证
- [ ] 敏感信息不暴露
- [ ] 错误消息用户友好
- [ ] 没有硬编码的敏感数据

## 常见问题和解决方案

### 1. 内存泄漏

**问题：** 游戏结束后内存持续增长

**解决方案：**
```javascript
// 确保正确清理资源
class Game {
  cleanup() {
    this.removeAllListeners();
    this.playerManager.cleanup();
    this.stateManager.cleanup();
  }
}
```

### 2. 异步操作错误

**问题：** 异步操作未正确处理错误

**解决方案：**
```javascript
// 使用try-catch包装异步操作
async function handlePlayerAction(player, action) {
  try {
    await this.stateManager.handleAction(player, action);
  } catch (error) {
    this.errorHandler.handle(error, { player, action });
  }
}
```

### 3. 状态不一致

**问题：** 多个组件状态不同步

**解决方案：**
```javascript
// 使用事件确保状态同步
class StateManager {
  changeState(newState) {
    this.currentState = newState;
    this.game.emit('stateChanged', { newState });
  }
}
```

## 持续改进

### 代码质量监控

- 定期运行静态分析工具
- 监控测试覆盖率变化
- 跟踪性能指标
- 收集错误统计

### 技术债务管理

- 定期识别技术债务
- 制定偿还计划
- 优先处理高风险债务
- 防止新债务产生

### 知识分享

- 定期代码审查会议
- 技术分享和培训
- 文档持续更新
- 最佳实践总结

## 工具和资源

### 开发工具

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试框架
- **Husky**: Git钩子管理

### 参考资源

- [JavaScript最佳实践](https://github.com/ryanmcdermott/clean-code-javascript)
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
- [测试最佳实践](https://github.com/goldbergyoni/javascript-testing-best-practices)

---

**维护者：** 开发团队  
**更新频率：** 每月或重大变更时  
**反馈渠道：** 项目Issue或团队会议
