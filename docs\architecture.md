# 狼人杀插件架构文档

**版本：** v0.6.0  
**更新日期：** 2025-01-16  
**架构状态：** 重构完成

## 架构概览

狼人杀插件采用模块化、事件驱动的架构设计，通过职责分离和依赖注入实现高内聚、低耦合的系统结构。

### 核心设计原则

1. **单一职责原则**：每个模块专注于特定功能
2. **开放封闭原则**：对扩展开放，对修改封闭
3. **依赖倒置原则**：依赖抽象而非具体实现
4. **接口隔离原则**：使用最小化接口
5. **事件驱动架构**：通过事件实现松耦合通信

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层 (Apps)                        │
├─────────────────────────────────────────────────────────────┤
│  GameRoles.js  │  GameStart.js  │  GameAction.js  │  ...   │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                      服务层 (Services)                      │
├─────────────────────────────────────────────────────────────┤
│  GameLobby  │  GameRegistry  │  PlayerQueryService  │ ...  │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                      核心层 (Core)                         │
├─────────────────────────────────────────────────────────────┤
│           Game (协调器)                                     │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │PlayerManager│StateManager │ErrorHandler │EventHandler │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                     基础层 (Foundation)                     │
├─────────────────────────────────────────────────────────────┤
│  Constants  │  ValidationUtils  │  GameConfig  │  Roles    │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件详解

### 1. Game类 (协调器)

**职责：** 游戏流程协调和组件管理
**重构变化：** 从庞大单体重构为轻量级协调器

```javascript
class Game extends EventEmitter {
  constructor({ playerManager, stateManager, eventHandler, ... }) {
    // 依赖注入，不直接创建依赖对象
  }
  
  // 委派方法，不直接实现业务逻辑
  addPlayer(player) {
    return this.playerManager.addPlayer(player);
  }
}
```

**关键特性：**
- 事件驱动通信
- 依赖注入设计
- 委派模式实现
- 资源生命周期管理

### 2. PlayerManager (玩家管理器)

**职责：** 玩家生命周期管理
**核心功能：**
- 玩家添加/删除/查询
- 角色分配和初始化
- 存活状态跟踪
- 死亡处理
- 缓存管理

```javascript
class PlayerManager {
  constructor(game) {
    this.players = new Map();
    this.roles = new Map();
    this.playerNumberMap = new Map();
    this._cacheSystem = { /* 缓存配置 */ };
  }
  
  async handlePlayerDeath(player, reason) {
    // 统一的死亡处理逻辑
  }
}
```

**性能优化：**
- 多级缓存系统
- 智能缓存失效
- 内存使用优化

### 3. StateManager (状态管理器)

**职责：** 游戏状态转换和管理
**核心功能：**
- 状态转换控制
- 游戏阶段管理
- 行动验证
- 状态历史记录

```javascript
class StateManager {
  constructor(game, stateMachine) {
    this.currentPhase = GAME_PHASES.WAITING;
    this.turn = 0;
    this.stateHistory = [];
  }
  
  async changeState(newState) {
    // 状态转换逻辑
    // 历史记录
    // 事件发布
  }
}
```

**状态机集成：**
- 包装原有StateMachine
- 提供高级接口
- 状态历史追踪

### 4. ErrorHandler (错误处理器)

**职责：** 统一错误处理和用户反馈
**核心功能：**
- 错误标准化
- 日志记录
- 用户反馈
- 错误统计

```javascript
class ErrorHandler {
  handle(error, context, e) {
    const standardError = this._standardizeError(error);
    this._logError(standardError);
    this._sendUserFeedback(standardError, e);
    return { success: true, error: standardError };
  }
}
```

**错误分类：**
- 按严重程度分级
- 按类别分组
- 用户友好消息映射

### 5. ValidationUtils (验证工具)

**职责：** 统一输入验证和业务规则检查
**核心功能：**
- 玩家验证
- 游戏状态验证
- 行动验证
- 批量验证

```javascript
class ValidationUtils {
  static validatePlayer(player, options = {}) {
    // 统一的玩家验证逻辑
    return { isValid: boolean, error?: GameError };
  }
}
```

## 数据流架构

### 请求处理流程

```
用户输入 → Apps → Services → Game → Managers → 业务逻辑
    ↓
错误处理 ← ErrorHandler ← ValidationUtils ← 验证层
    ↓
用户反馈 ← EventHandler ← 事件系统 ← 结果处理
```

### 事件流架构

```
业务事件 → Game.emit() → EventHandler → 消息发送
    ↓
系统事件 → 日志记录 → 监控系统 → 告警机制
```

## 配置管理架构

### 配置层次结构

```
GameConfig (配置管理器)
├── 验证规则 (ValidationRules)
├── 热重载 (HotReload)
├── 配置键名 (CONFIG_KEYS)
└── 默认值 (DefaultValues)
```

### 配置验证流程

```
配置变更 → 验证器 → 类型检查 → 范围检查 → 应用配置
    ↓
验证失败 → 错误记录 → 回滚配置 → 用户通知
```

## 角色系统架构

### 角色继承体系

```
Role (基类)
├── WolfRole (狼人)
├── VillagerRole (村民)
├── ProphetRole (预言家)
├── WitchRole (女巫)
├── HunterRole (猎人)
└── GuardRole (守卫)
```

### 角色工厂模式

```javascript
class RoleFactory {
  static createRole(roleName, game, player) {
    const RoleClass = this.roleMap[roleName];
    return new RoleClass(game, player);
  }
}
```

## 缓存架构

### 多级缓存设计

```
L1缓存 (内存) → L2缓存 (进程) → L3缓存 (持久化)
    ↓
缓存策略: LRU + TTL + 智能失效
```

### 缓存失效策略

- **主动失效**：数据变更时立即失效
- **被动失效**：TTL到期自动失效
- **智能失效**：基于访问模式的预测失效

## 安全架构

### 输入验证层

```
用户输入 → 格式验证 → 业务验证 → 安全检查 → 处理逻辑
```

### 错误处理安全

- 敏感信息过滤
- 错误信息分级
- 审计日志记录

## 性能架构

### 性能优化策略

1. **缓存优化**：多级缓存减少计算
2. **对象池化**：减少GC压力
3. **懒加载**：按需加载资源
4. **批量处理**：减少I/O操作

### 内存管理

```
创建 → 使用 → 缓存 → 清理 → 回收
    ↓
监控 → 告警 → 优化 → 验证
```

## 扩展性设计

### 插件化架构

```
核心系统 + 插件接口 = 可扩展系统
    ↓
新功能 → 插件开发 → 接口注册 → 功能集成
```

### 微服务演进路径

```
单体架构 → 模块化架构 → 服务化架构 → 微服务架构
```

## 部署架构

### 单机部署

```
Yunzai框架 → 插件加载 → 内存运行 → 本地存储
```

### 分布式部署 (未来)

```
负载均衡 → 多实例 → 共享存储 → 消息队列
```

## 监控架构

### 监控指标

- **性能指标**：响应时间、吞吐量、资源使用
- **业务指标**：游戏数量、玩家活跃度、错误率
- **系统指标**：内存使用、CPU使用、网络I/O

### 告警机制

```
指标收集 → 阈值检查 → 告警触发 → 通知发送
```

## 总结

重构后的架构具有以下优势：

1. **高内聚低耦合**：模块职责清晰，依赖关系简单
2. **可测试性强**：组件可独立测试，模拟容易
3. **可扩展性好**：新功能易于添加，现有功能易于修改
4. **性能优秀**：缓存优化，资源管理完善
5. **安全可靠**：错误处理完善，输入验证严格

该架构为后续的功能扩展和性能优化奠定了坚实基础。
