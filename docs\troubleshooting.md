# 故障排除指南

**版本：** v0.6.0  
**更新日期：** 2025-01-16  
**适用范围：** 开发者和运维人员

## 概述

本指南提供了狼人杀插件常见问题的诊断和解决方案，帮助快速定位和修复问题。

## 快速诊断

### 系统健康检查

```bash
# 检查Node.js版本
node --version  # 应该 >= 16.0.0

# 检查内存使用
node -e "console.log(process.memoryUsage())"

# 检查插件状态
npm test  # 运行测试套件
```

### 日志级别设置

```javascript
// 开启调试模式
process.env.DEBUG = 'werewolf:*';
process.env.LOG_LEVEL = 'debug';
```

## 常见问题分类

### 🔴 启动问题

#### 问题1：插件无法加载

**症状：**
```
Error: Cannot find module './model/core/Game.js'
```

**原因：**
- 文件路径错误
- ES模块配置问题
- 依赖缺失

**解决方案：**
```bash
# 1. 检查文件是否存在
ls -la model/core/Game.js

# 2. 检查package.json配置
grep -n "type.*module" package.json

# 3. 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 问题2：配置文件错误

**症状：**
```
Error: Invalid configuration: minPlayers must be a number
```

**原因：**
- 配置文件格式错误
- 配置验证失败
- 默认值缺失

**解决方案：**
```javascript
// 检查配置文件
import { GameConfig } from './components/GameConfig.js';

const config = new GameConfig();
console.log('配置验证:', config.validateConfig('minPlayers', 6));
```

### 🟡 运行时问题

#### 问题3：内存泄漏

**症状：**
- 内存使用持续增长
- 游戏结束后内存不释放
- 系统变慢

**诊断：**
```javascript
// 内存监控
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('内存使用:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB'
  });
}, 5000);
```

**解决方案：**
```javascript
// 确保正确清理
class Game {
  cleanup() {
    // 移除所有事件监听器
    this.removeAllListeners();
    
    // 清理管理器
    this.playerManager?.cleanup();
    this.stateManager?.cleanup();
    
    // 清理引用
    this.playerManager = null;
    this.stateManager = null;
  }
}
```

#### 问题4：状态不一致

**症状：**
- 玩家状态显示错误
- 游戏阶段混乱
- 行动验证失败

**诊断：**
```javascript
// 状态调试
console.log('游戏状态:', {
  phase: game.stateManager.getCurrentPhase(),
  turn: game.stateManager.getCurrentTurn(),
  playerCount: game.playerManager.getPlayerCount(),
  alivePlayers: game.getAlivePlayers().length
});
```

**解决方案：**
```javascript
// 重置游戏状态
game.stateManager.cleanup();
game.stateManager.initializeState();
```

### 🟢 性能问题

#### 问题5：响应缓慢

**症状：**
- 命令响应时间长
- 游戏操作延迟
- CPU使用率高

**诊断：**
```javascript
// 性能监控
console.time('操作耗时');
await game.handleAction(player, action, target);
console.timeEnd('操作耗时');
```

**解决方案：**
```javascript
// 启用缓存
class PlayerManager {
  getAlivePlayers(options = {}) {
    const cacheKey = JSON.stringify(options);
    if (this._cache.has(cacheKey)) {
      return this._cache.get(cacheKey);
    }
    // ... 计算结果并缓存
  }
}
```

#### 问题6：缓存失效

**症状：**
- 数据不更新
- 显示过期信息
- 状态同步问题

**解决方案：**
```javascript
// 手动清理缓存
game.playerManager._invalidateCache();

// 检查缓存状态
console.log('缓存状态:', game.playerManager._cacheSystem);
```

## 错误代码参考

### 验证错误 (E11xx)

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| E1100 | 玩家验证失败 | 检查玩家对象是否有效，是否存活 |
| E1101 | 游戏号码无效 | 确认号码在有效范围内(1-20) |
| E1102 | 角色验证失败 | 检查角色是否正确分配 |

### 状态错误 (E12xx)

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| E1200 | 无效操作 | 检查当前游戏阶段是否允许该操作 |
| E1201 | 游戏状态错误 | 重新初始化游戏状态 |

### 系统错误 (E10xx)

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| E1000 | 未知错误 | 检查日志获取详细信息 |
| E1001 | 参数错误 | 验证传入参数的类型和值 |
| E1002 | 空引用错误 | 检查对象是否正确初始化 |

## 调试技巧

### 1. 启用详细日志

```javascript
// 设置环境变量
process.env.DEBUG = 'werewolf:*';
process.env.LOG_LEVEL = 'debug';

// 或在代码中设置
import debug from 'debug';
const log = debug('werewolf:game');
log('游戏状态变更:', newState);
```

### 2. 使用断点调试

```javascript
// 在关键位置添加断点
debugger;

// 或使用条件断点
if (player.id === 'problematic-player') {
  debugger;
}
```

### 3. 状态快照

```javascript
// 保存状态快照用于分析
function captureGameSnapshot(game) {
  return {
    timestamp: Date.now(),
    phase: game.stateManager.getCurrentPhase(),
    turn: game.stateManager.getCurrentTurn(),
    players: Array.from(game.playerManager.getAllPlayers().values()),
    stateHistory: game.stateManager.getStateHistory()
  };
}
```

## 性能优化

### 1. 内存优化

```javascript
// 定期清理不需要的数据
setInterval(() => {
  // 清理过期的游戏实例
  GameRegistry.cleanupExpiredGames();
  
  // 强制垃圾回收（开发环境）
  if (global.gc && process.env.NODE_ENV === 'development') {
    global.gc();
  }
}, 60000);
```

### 2. 缓存优化

```javascript
// 智能缓存失效
class SmartCache {
  set(key, value, ttl = 60000) {
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl,
      hits: 0
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item || Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    item.hits++;
    return item.value;
  }
}
```

### 3. 批量操作

```javascript
// 批量处理减少开销
class BatchProcessor {
  constructor() {
    this.queue = [];
    this.timer = null;
  }
  
  add(operation) {
    this.queue.push(operation);
    if (!this.timer) {
      this.timer = setTimeout(() => this.process(), 10);
    }
  }
  
  process() {
    const operations = this.queue.splice(0);
    this.timer = null;
    
    // 批量执行
    operations.forEach(op => op());
  }
}
```

## 监控和告警

### 1. 健康检查端点

```javascript
// 添加健康检查
app.get('/health', (req, res) => {
  const health = {
    status: 'ok',
    timestamp: Date.now(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    activeGames: GameRegistry.getActiveGameCount(),
    errors: ErrorHandler.getStats()
  };
  
  res.json(health);
});
```

### 2. 错误统计

```javascript
// 错误统计和告警
class ErrorMonitor {
  constructor() {
    this.errorCounts = new Map();
    this.alertThreshold = 10; // 10分钟内超过阈值告警
  }
  
  recordError(error) {
    const key = `${error.code}_${Math.floor(Date.now() / 600000)}`;
    const count = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, count + 1);
    
    if (count + 1 >= this.alertThreshold) {
      this.sendAlert(error.code, count + 1);
    }
  }
}
```

## 恢复策略

### 1. 自动恢复

```javascript
// 自动重试机制
async function withRetry(operation, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 2. 状态恢复

```javascript
// 从快照恢复游戏状态
function restoreGameFromSnapshot(game, snapshot) {
  // 恢复玩家状态
  snapshot.players.forEach(playerData => {
    const player = game.playerManager.getPlayer(playerData.id);
    if (player) {
      Object.assign(player, playerData);
    }
  });
  
  // 恢复游戏状态
  game.stateManager.currentPhase = snapshot.phase;
  game.stateManager.turn = snapshot.turn;
}
```

## 预防措施

### 1. 输入验证

```javascript
// 严格的输入验证
function validateInput(input, schema) {
  const errors = [];
  
  for (const [key, rules] of Object.entries(schema)) {
    const value = input[key];
    
    if (rules.required && (value === undefined || value === null)) {
      errors.push(`${key} is required`);
    }
    
    if (value !== undefined && rules.type && typeof value !== rules.type) {
      errors.push(`${key} must be ${rules.type}`);
    }
  }
  
  return errors;
}
```

### 2. 资源限制

```javascript
// 设置资源限制
const MAX_GAMES_PER_GROUP = 1;
const MAX_PLAYERS_PER_GAME = 20;
const GAME_TIMEOUT = 3600000; // 1小时

// 检查限制
if (GameRegistry.getGamesByGroup(groupId).length >= MAX_GAMES_PER_GROUP) {
  throw new Error('该群组已达到游戏数量限制');
}
```

## 联系支持

### 问题报告

当遇到无法解决的问题时，请提供以下信息：

1. **环境信息**
   - Node.js版本
   - 插件版本
   - 操作系统

2. **错误信息**
   - 完整的错误堆栈
   - 错误发生时间
   - 复现步骤

3. **系统状态**
   - 内存使用情况
   - 活跃游戏数量
   - 最近的操作日志

### 紧急联系

- **严重问题**：立即联系开发团队
- **一般问题**：提交Issue到项目仓库
- **功能请求**：通过正常渠道提交

---

**维护者：** 开发团队  
**更新频率：** 发现新问题时及时更新  
**版本历史：** 记录在Git提交历史中
