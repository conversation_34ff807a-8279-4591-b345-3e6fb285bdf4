import { ValidationUtils } from '../utils/ValidationUtils.js';

export class Role {
  constructor(game, player) {
    this.game = game;
    this.player = player;
  }

  // 获取角色名称
  getName() {
    return this.player.role;
  }

  /**
   * 获取角色阵营
   * @returns {string} 角色阵营
   */
  async getCamp() {
    const { RoleFactory } = await import('./RoleFactory.js');
    return RoleFactory.getRoleCamp(this.player.role);
  }

  // 检查是否可以在当前阶段行动
  canAct(state) {
    return false;
  }

  // 执行行动
  async act(target) {
    throw new Error("需要在子类中实现act方法");
  }

  // 获取行动提示
  getActionPrompt() {
    return "";
  }

  // 验证目标是否合法
  isValidTarget(target) {
    // 使用 ValidationUtils 进行统一验证
    const validation = ValidationUtils.validateTarget(target);

    if (!validation.isValid) {
      console.debug(`isValidTarget: ${validation.error.message}`);
      return false;
    }

    return true;
  }

  // 获取存活玩家列表
  getAlivePlayersList() {
    // 使用 ValidationUtils 验证游戏对象
    if (!this.game) {
      console.error('getAlivePlayersList: game 对象未初始化');
      return "游戏未初始化";
    }

    if (!this.game.players || typeof this.game.players.values !== 'function') {
      console.error('getAlivePlayersList: game.players 无效');
      return "玩家数据无效";
    }

    try {
      const players = Array.from(this.game.players.values())
        .filter((player) => {
          // 使用 ValidationUtils 验证玩家对象
          const validation = ValidationUtils.validatePlayer(player, { checkAlive: true });
          if (!validation.isValid) {
            console.debug(`getAlivePlayersList: 跳过无效玩家: ${validation.error.message}`);
            return false;
          }
          return true;
        })
        .map((player) => {
          // 安全的字符串构建
          const gameNumber = player.gameNumber || '?';
          const name = player.name || '未知';
          return `${gameNumber}号 ${name}`;
        });

      return players.length > 0 ? players.join("\n") : "暂无存活玩家";
    } catch (error) {
      console.error('getAlivePlayersList: 获取玩家列表时发生错误:', error);
      return "获取玩家列表失败";
    }
  }

}
