/**
 * 游戏注册表 - 管理游戏实例的生命周期
 * 增强版本，包含内存泄漏防护和资源清理机制
 */
export class GameRegistry {
    // 静态游戏实例存储
    static games = new Map();

    // 游戏创建时间记录，用于自动清理
    static gameTimestamps = new Map();

    // 配置选项
    static config = {
        maxGameAge: 24 * 60 * 60 * 1000, // 24小时后自动清理
        maxGames: 100, // 最大游戏数量
        cleanupInterval: 60 * 60 * 1000, // 1小时清理一次
        enableAutoCleanup: true
    };

    // 清理定时器
    static cleanupTimer = null;

    /**
     * 初始化注册表，启动自动清理机制
     */
    static initialize() {
        if (this.config.enableAutoCleanup && !this.cleanupTimer) {
            this.cleanupTimer = setInterval(() => {
                this.performAutoCleanup();
            }, this.config.cleanupInterval);

            console.log('[GameRegistry] 自动清理机制已启动');
        }
    }

    /**
     * 获取游戏实例
     */
    static getGame(groupId) {
        const game = this.games.get(groupId);

        // 更新访问时间
        if (game) {
            this.gameTimestamps.set(groupId, Date.now());
        }

        return game;
    }

    /**
     * 添加游戏实例
     */
    static addGame(groupId, gameInstance) {
        // 检查是否超过最大游戏数量
        if (this.games.size >= this.config.maxGames) {
            console.warn(`[GameRegistry] 达到最大游戏数量限制 (${this.config.maxGames})，执行清理`);
            this.performAutoCleanup();

            // 如果清理后仍然超限，拒绝添加
            if (this.games.size >= this.config.maxGames) {
                throw new Error('游戏数量已达上限，请稍后再试');
            }
        }

        this.games.set(groupId, gameInstance);
        this.gameTimestamps.set(groupId, Date.now());

        // 确保自动清理机制已启动
        this.initialize();

        console.log(`[GameRegistry] 游戏已添加: ${groupId}, 当前游戏数: ${this.games.size}`);
    }

    /**
     * 移除游戏实例并清理相关资源
     */
    static removeGame(groupId) {
        const game = this.games.get(groupId);

        if (game) {
            // 清理游戏资源
            this.cleanupGameResources(game);

            // 从注册表中移除
            this.games.delete(groupId);
            this.gameTimestamps.delete(groupId);

            console.log(`[GameRegistry] 游戏已移除: ${groupId}, 当前游戏数: ${this.games.size}`);
        }
    }

    /**
     * 检查游戏是否存在
     */
    static hasGame(groupId) {
        return this.games.has(groupId);
    }

    /**
     * 清理游戏相关资源
     * @private
     */
    static cleanupGameResources(game) {
        try {
            // 清理事件监听器
            if (game && typeof game.removeAllListeners === 'function') {
                game.removeAllListeners();
            }

            // 清理游戏内部资源
            if (game && typeof game.cleanup === 'function') {
                game.cleanup();
            }

            // 清理角色静态数据
            this.cleanupRoleStaticData();

        } catch (error) {
            console.error('[GameRegistry] 清理游戏资源时发生错误:', error);
        }
    }

    /**
     * 清理角色静态数据
     * @private
     */
    static cleanupRoleStaticData() {
        try {
            // 动态导入并清理 WolfRole 静态数据
            import('../roles/WolfRole.js').then(({ WolfRole }) => {
                if (WolfRole && typeof WolfRole.cleanup === 'function') {
                    WolfRole.cleanup();
                }
            }).catch(error => {
                console.debug('[GameRegistry] 清理WolfRole静态数据时出错:', error.message);
            });
        } catch (error) {
            // 模块可能未加载，忽略错误
            console.debug('[GameRegistry] 清理角色静态数据时出现错误:', error.message);
        }
    }

    /**
     * 执行自动清理
     * @private
     */
    static performAutoCleanup() {
        const now = Date.now();
        const expiredGames = [];

        // 查找过期游戏
        for (const [groupId, timestamp] of this.gameTimestamps.entries()) {
            if (now - timestamp > this.config.maxGameAge) {
                expiredGames.push(groupId);
            }
        }

        // 清理过期游戏
        if (expiredGames.length > 0) {
            console.log(`[GameRegistry] 清理 ${expiredGames.length} 个过期游戏:`, expiredGames);

            for (const groupId of expiredGames) {
                this.removeGame(groupId);
            }
        }

        console.log(`[GameRegistry] 自动清理完成，当前游戏数: ${this.games.size}`);
    }

    /**
     * 获取注册表统计信息
     */
    static getStats() {
        return {
            totalGames: this.games.size,
            maxGames: this.config.maxGames,
            oldestGameAge: this.getOldestGameAge(),
            autoCleanupEnabled: this.config.enableAutoCleanup
        };
    }

    /**
     * 获取最老游戏的年龄
     * @private
     */
    static getOldestGameAge() {
        if (this.gameTimestamps.size === 0) return 0;

        const now = Date.now();
        let oldestAge = 0;

        for (const timestamp of this.gameTimestamps.values()) {
            const age = now - timestamp;
            if (age > oldestAge) {
                oldestAge = age;
            }
        }

        return oldestAge;
    }

    /**
     * 手动清理所有游戏（用于测试或紧急情况）
     */
    static clearAll() {
        console.log(`[GameRegistry] 手动清理所有游戏，共 ${this.games.size} 个`);

        for (const [groupId] of this.games.entries()) {
            this.removeGame(groupId);
        }
    }

    /**
     * 关闭注册表，清理所有资源
     */
    static shutdown() {
        // 停止自动清理定时器
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }

        // 清理所有游戏
        this.clearAll();

        console.log('[GameRegistry] 注册表已关闭');
    }
}