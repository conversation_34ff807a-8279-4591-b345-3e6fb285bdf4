# 狼人杀角色行动时序图

**版本：** v0.6.0  
**创建日期：** 2025-01-16  
**基于：** model/action/NightState.js 和各角色实现

## 概述

本文档基于 `model/action/NightState.js` 中的 `actionQueue` 配置和各角色的具体实现，使用 Mermaid 语法创建详细的角色行动时序图，展示夜晚阶段各角色的行动顺序和交互关系。

## 1. 夜晚阶段角色行动时序

### 1.1 基本行动顺序

**ActionQueue 配置：**
```javascript
this.actionQueue = ['GuardRole', 'ProphetRole', 'WolfRole', 'WitchRole']
```

### 1.2 完整夜晚行动时序图

```mermaid
sequenceDiagram
    participant NS as NightState
    participant G as GuardRole(守卫)
    participant P as ProphetRole(预言家)
    participant W as WolfRole(狼人)
    participant Witch as WitchRole(女巫)
    participant Game as Game
    participant Players as 所有玩家

    Note over NS: 夜晚阶段开始
    NS->>Players: 发送"天黑请闭眼"通知
    NS->>NS: 初始化actionQueue
    
    Note over NS,G: 第一阶段：守卫行动
    NS->>G: 通知守卫行动
    G->>G: 检查canAct()
    G->>Players: 发送行动提示
    G->>G: 等待守护选择
    G->>Game: 设置target.protected = true
    G->>NS: 行动完成
    NS->>NS: completedRoles.add('GuardRole')
    
    Note over NS,P: 第二阶段：预言家行动
    NS->>P: 通知预言家行动
    P->>P: 检查canAct()
    P->>Players: 发送行动提示(含历史记录)
    P->>P: 等待查验选择
    P->>P: 执行查验逻辑
    P->>P: 记录查验结果到checkHistory
    P->>Players: 发送查验结果
    P->>NS: 行动完成
    NS->>NS: completedRoles.add('ProphetRole')
    
    Note over NS,W: 第三阶段：狼人行动(复杂投票机制)
    NS->>W: 通知狼人行动
    W->>W: 检查canAct()
    W->>Players: 发送行动提示(含队友信息)
    
    loop 狼人投票过程
        W->>W: 等待投票选择
        W->>W: handleVote(targetId)
        W->>W: 更新WolfRole.wolfVotes
        W->>Players: 通知其他狼人投票更新
        W->>W: 检查isAllWolvesVoted()
        alt 所有狼人已投票
            W->>W: tallyVotes() 统计结果
            W->>W: 设置WolfRole.wolfKillTarget
            W->>Players: 通知投票结果
            break 投票完成
        end
    end
    
    alt 有击杀目标
        W->>Game: 执行击杀 act(target, 'kill')
        Game->>Game: 检查target.protected
        alt 目标被保护
            Game->>Players: 击杀失败通知
        else 目标未被保护
            Game->>Game: handlePlayerDeath(target, 'WOLF_KILL')
        end
    else 空刀或平票
        W->>Players: 无击杀通知
    end
    
    W->>NS: 行动完成
    NS->>NS: completedRoles.add('WolfRole')
    
    Note over NS,Witch: 第四阶段：女巫行动
    NS->>Witch: 通知女巫行动
    Witch->>Witch: 检查canAct()
    Witch->>Witch: 检查hasAntidote && hasPoison
    
    alt 女巫有药可用
        Witch->>Game: 获取狼人击杀信息
        Witch->>Players: 发送行动提示(含击杀信息)
        Witch->>Witch: 等待用药选择
        
        alt 选择救人
            Witch->>Witch: 检查isValidTarget(target, 'save')
            Witch->>Game: 复活目标 target.isAlive = true
            Witch->>Witch: hasAntidote = false
            Witch->>Players: 救人成功通知
        else 选择毒人
            Witch->>Witch: 检查isValidTarget(target, 'poison')
            Witch->>Game: handlePlayerDeath(target, 'POISON')
            Witch->>Witch: hasPoison = false
            Witch->>Players: 毒人成功通知
        else 选择跳过
            Witch->>Players: 跳过通知
        end
    else 女巫无药可用
        Witch->>Players: 无药可用通知
        Witch->>Witch: 自动跳过
    end
    
    Witch->>NS: 行动完成
    NS->>NS: completedRoles.add('WitchRole')
    
    Note over NS: 夜晚阶段结束
    NS->>NS: finishNightPhase()
    NS->>Game: 清理保护状态
    NS->>Game: 状态转换到DayState
```

### 1.3 角色行动依赖关系图

```mermaid
graph TD
    A[夜晚开始] --> B[守卫行动]
    B --> C[预言家行动]
    C --> D[狼人行动]
    D --> E[女巫行动]
    E --> F[夜晚结束]
    
    B1[守护目标] --> D1[狼人击杀检查]
    D2[狼人击杀结果] --> E1[女巫救人选择]
    D2 --> E2[女巫毒人选择]
    
    style A fill:#2d3748,stroke:#4a5568,color:#fff
    style F fill:#2d3748,stroke:#4a5568,color:#fff
    style B fill:#68d391,stroke:#38a169,color:#000
    style C fill:#4299e1,stroke:#3182ce,color:#fff
    style D fill:#fc8181,stroke:#e53e3e,color:#fff
    style E fill:#a78bfa,stroke:#7c3aed,color:#fff
```

## 2. 角色特殊技能和交互机制

### 2.1 守卫角色机制

**核心特点：**
- 行动顺序：第一位
- 限制条件：不能连续两晚守护同一人
- 效果持续：整个夜晚阶段
- 交互影响：影响狼人击杀结果

**行动流程：**
```mermaid
flowchart TD
    A[守卫收到行动通知] --> B{检查canAct}
    B -->|否| C[跳过行动]
    B -->|是| D[显示可守护目标列表]
    D --> E[显示上次守护信息]
    E --> F[等待玩家选择]
    F --> G{验证目标合法性}
    G -->|非法| H[提示错误信息]
    G -->|合法| I[设置target.protected = true]
    I --> J[记录lastProtectedId]
    J --> K[发送成功确认]
    K --> L[行动完成]
    H --> F
    C --> L
```

### 2.2 预言家角色机制

**核心特点：**
- 行动顺序：第二位
- 查验能力：识别目标阵营(好人/狼人)
- 历史记录：保存所有查验结果
- 信息获取：为白天发言提供依据

**查验流程：**
```mermaid
flowchart TD
    A[预言家收到行动通知] --> B[显示历史查验记录]
    B --> C[显示可查验目标列表]
    C --> D[等待玩家选择]
    D --> E{验证目标合法性}
    E -->|非法| F[提示错误信息]
    E -->|合法| G[获取目标角色信息]
    G --> H[判断阵营: getCamp()]
    H --> I{目标是否为狼人}
    I -->|是| J[返回"狼人"结果]
    I -->|否| K[返回"好人"结果]
    J --> L[记录到checkHistory]
    K --> L
    L --> M[发送查验结果]
    M --> N[行动完成]
    F --> D
```

### 2.3 狼人角色机制

**核心特点：**
- 行动顺序：第三位
- 投票机制：多狼协商投票
- 队内沟通：支持实时讨论
- 击杀执行：统一行动

**投票协商流程：**
```mermaid
sequenceDiagram
    participant W1 as 狼人1
    participant W2 as 狼人2
    participant WS as WolfRole.wolfVotes
    participant Game as Game

    Note over W1,W2: 狼人投票阶段
    
    W1->>W1: 收到行动通知
    W2->>W2: 收到行动通知
    
    par 狼人讨论
        W1->>W2: #讨论 我觉得应该杀3号
        W2->>W1: #讨论 同意，3号很可疑
    end
    
    W1->>WS: handleVote(target3.id)
    WS->>W2: 通知投票更新
    
    W2->>WS: handleVote(target3.id)
    WS->>W1: 通知投票更新
    
    WS->>WS: 检查isAllWolvesVoted()
    WS->>WS: tallyVotes() 统计结果
    WS->>W1: 通知最终结果
    WS->>W2: 通知最终结果
    
    alt 击杀目标确定
        WS->>Game: 执行击杀
    else 平票或空刀
        WS->>Game: 无击杀
    end
```

### 2.4 女巫角色机制

**核心特点：**
- 行动顺序：第四位(最后)
- 双重药水：解药(救人) + 毒药(杀人)
- 信息获取：知晓狼人击杀结果
- 使用限制：每种药水只能用一次，不能同时使用

**用药决策流程：**
```mermaid
flowchart TD
    A[女巫收到行动通知] --> B{检查药水状态}
    B -->|无药可用| C[自动跳过]
    B -->|有药可用| D[获取狼人击杀信息]
    D --> E[显示用药选项]
    E --> F[等待玩家选择]
    F --> G{选择类型}
    
    G -->|救人| H{检查解药可用性}
    H -->|无解药| I[提示无解药]
    H -->|有解药| J{验证救人目标}
    J -->|非法| K[提示错误]
    J -->|合法| L[使用解药救人]
    L --> M[hasAntidote = false]
    M --> N[target.isAlive = true]
    N --> O[发送救人成功通知]
    
    G -->|毒人| P{检查毒药可用性}
    P -->|无毒药| Q[提示无毒药]
    P -->|有毒药| R{验证毒人目标}
    R -->|非法| S[提示错误]
    R -->|合法| T[使用毒药毒人]
    T --> U[hasPoison = false]
    U --> V[handlePlayerDeath(target, 'POISON')]
    V --> W[发送毒人成功通知]
    
    G -->|跳过| X[发送跳过通知]
    
    O --> Y[行动完成]
    W --> Y
    X --> Y
    C --> Y
    I --> F
    K --> F
    Q --> F
    S --> F
```

## 3. 超时处理和异常情况

### 3.1 超时处理机制

```mermaid
sequenceDiagram
    participant NS as NightState
    participant Timer as 超时计时器
    participant Roles as 未行动角色
    participant Game as Game

    Timer->>NS: onTimeout() 触发
    NS->>NS: 检查currentActionRole
    
    alt 有当前行动角色
        NS->>Roles: 获取未行动玩家列表
        loop 为每个未行动玩家
            alt 狼人角色
                NS->>Roles: 设置默认弃权票
            else 其他角色
                NS->>Roles: 设置默认跳过行动
            end
        end
        NS->>NS: completedRoles.add(currentActionRole)
    end
    
    NS->>NS: 标记所有剩余角色为已完成
    NS->>Game: finishNightPhase()
```

### 3.2 异常情况处理

**常见异常情况：**

1. **玩家离线/无响应**
   - 超时后自动设置默认行动
   - 狼人默认弃权，其他角色默认跳过

2. **私聊消息发送失败**
   - 捕获异常并在群内提示
   - 不影响游戏流程继续

3. **角色不存在或已死亡**
   - 自动跳过该角色的行动轮次
   - 继续下一个角色的行动

4. **状态转换异常**
   - 记录错误日志
   - 尝试恢复到稳定状态

### 3.3 错误恢复流程

```mermaid
flowchart TD
    A[检测到异常] --> B{异常类型}
    
    B -->|玩家离线| C[设置默认行动]
    B -->|消息发送失败| D[群内提示 + 继续流程]
    B -->|角色不存在| E[跳过该角色]
    B -->|状态异常| F[记录日志 + 状态恢复]
    
    C --> G[继续下一角色]
    D --> G
    E --> G
    F --> G
    
    G --> H{所有角色完成?}
    H -->|是| I[结束夜晚阶段]
    H -->|否| J[继续角色行动]
    
    J --> A
```

## 4. 角色行动状态管理

### 4.1 行动状态追踪

**NightState 状态管理：**
```javascript
// 核心状态变量
this.actionQueue = ['GuardRole', 'ProphetRole', 'WolfRole', 'WitchRole']
this.currentActionRole = null
this.actionLock = false
this.completedRoles = new Set()
this.roleActions = new Map()
```

### 4.2 状态转换条件

```mermaid
stateDiagram-v2
    [*] --> WaitingForGuard : 夜晚开始
    WaitingForGuard --> WaitingForProphet : 守卫行动完成
    WaitingForProphet --> WaitingForWolf : 预言家行动完成
    WaitingForWolf --> WaitingForWitch : 狼人行动完成
    WaitingForWitch --> NightEnd : 女巫行动完成
    NightEnd --> [*] : 进入白天阶段
    
    WaitingForGuard --> Timeout : 超时
    WaitingForProphet --> Timeout : 超时
    WaitingForWolf --> Timeout : 超时
    WaitingForWitch --> Timeout : 超时
    Timeout --> NightEnd : 处理默认行动
```

## 总结

狼人杀夜晚阶段的角色行动时序设计具有以下特点：

1. **严格的行动顺序**：Guard → Prophet → Wolf → Witch，确保游戏逻辑的正确性
2. **复杂的交互机制**：角色间的行动结果相互影响，形成完整的游戏生态
3. **完善的异常处理**：超时、离线、错误等情况都有相应的处理机制
4. **灵活的状态管理**：通过状态机和队列管理确保流程的可控性
5. **丰富的策略空间**：每个角色都有独特的技能和决策点

这种设计确保了游戏的公平性、可玩性和稳定性，为复杂的狼人杀游戏提供了坚实的技术基础。
